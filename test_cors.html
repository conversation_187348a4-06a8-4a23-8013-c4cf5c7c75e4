<!DOCTYPE html>
<html>
<head>
    <title>CORS Test</title>
</head>
<body>
    <h1>CORS Test</h1>
    <button onclick="testCORS()">Test CORS</button>
    <div id="result"></div>

    <script>
        async function testCORS() {
            const resultDiv = document.getElementById('result');
            try {
                const response = await fetch('http://localhost:8000/health', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    resultDiv.innerHTML = '<p style="color: green;">CORS working! Response: ' + JSON.stringify(data) + '</p>';
                } else {
                    resultDiv.innerHTML = '<p style="color: red;">HTTP Error: ' + response.status + '</p>';
                }
            } catch (error) {
                resultDiv.innerHTML = '<p style="color: red;">CORS Error: ' + error.message + '</p>';
            }
        }
    </script>
</body>
</html>
