# 环境变量
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST
venv/
env/
ENV/

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# 构建输出
/frontend/dist/
/frontend/build/

# 日志
*.log
logs/

# 数据库
*.db
*.sqlite

# 上传文件
uploads/
/backend/uploads/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Docker
.dockerignore

# 临时文件
*.tmp
*.temp

# 测试覆盖率
coverage/
.nyc_output/
.coverage
htmlcov/

# 缓存
.cache/
.parcel-cache/
